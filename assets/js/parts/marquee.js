$(document).ready(function(){
    
    $(document).off("initPage.marquee").on("initPage.marquee", function (){
        initMarquees();
    });
    
});

function initMarquees(){
    $('[data-marquee]').each(function (i, el) {
        var speed = $(el).attr('data-marquee-speed') * ($(el).find(".itemsContainer").width() / $(window).width());
        if ($(window).width() <= 580) {
            speed = speed * 0.25;
        } else if ($(window).width() <= 1080) {
            speed = speed * 0.5;
        }

        for (var i = 0; i < 3; i++) {
            var clonedMarqueeContent = $(el).find(".itemsContainer:first-child").clone();
            $(el).find(".marqueeScroll").append(clonedMarqueeContent);
        }

        if ($(el).attr('data-marquee-direction') == 'right') {
            var direction = -1;
        } else {
            var direction = 1;
        }

        var timeline = gsap.to($(el).find('.itemsContainer'), {
            xPercent: -100,
            repeat: -1,
            duration: speed,
            ease: "linear",
            paused: true
        }).totalProgress(0.5);

        ScrollTrigger.create({
            trigger: $(el),
            start: "top bottom",
            end: "bottom top",
            onUpdate(self) {
                if (self.direction !== direction) {
                    direction = direction * -1;
                    if ($(el).attr('data-marquee-direction') == 'right') {
                        gsap.to([timeline], { timeScale: (direction * -1), overwrite: true});
                    } else {
                        gsap.to([timeline], { timeScale: direction, overwrite: true });
                    }
                }
            },
            onEnter(){
                timeline.play();
            },
            onEnterBack(){
                timeline.play();
            },
            onLeave(){
                timeline.pause();
            },
            onLeaveBack(){
                timeline.pause();
            }
        });

        // Wrapper animation on scroll, extra speed
        $(el).each(function (i, el) {
            
            var scrollSpeed = $(this).attr('data-marquee-scroll-speed');
            var scrollSpeedTimeline = gsap.timeline({
                scrollTrigger: {
                    trigger: $(el),
                    start: "top bottom",
                    end: "bottom top",
                    scrub: 1
                }
            });
            
            if ($(el).attr('data-marquee-direction') == 'left') {
              scrollSpeedTimeline.fromTo($(el), {x: scrollSpeed + "%"}, {x: (scrollSpeed * -1) + "%", ease: "none"});
            }
            
            if ($(el).attr('data-marquee-direction') == 'right') {
                scrollSpeedTimeline.fromTo($(el), {x: (scrollSpeed * -1) + "%"}, {x: scrollSpeed + "%",ease: "none"});
            }
        });
               
        if($(el).attr('data-marquee-swipe')){

            var swipeSpeed = 0;
            
            $(el).each( function(i, el) {
                var hammertime = new Hammer($(el).get(0));
                hammertime.get('pan').set({ direction: Hammer.DIRECTION_HORIZONTAL });    
                // listen to events...
                hammertime.on("panleft panright", function(ev) { 
                    swipeSpeed = (ev.velocityX * 5) * -1;
                    timeline.timeScale(swipeSpeed);
                });
                hammertime.on("panend", function(ev) { 
                    if ($(el).attr('data-marquee-direction') == 'right') {
                        gsap.to([timeline], .6,{ timeScale: (direction * -1), overwrite: true});
                    } else {
                        gsap.to([timeline], .6, { timeScale: direction, overwrite: true });
                    }
                });
            });
        }
    });
}