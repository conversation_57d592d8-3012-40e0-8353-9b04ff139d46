// out : false
.homeAboutBlock {
    padding-top: @vw100 * 6;
    position: relative;
    .stickyItem, .imageWrapper {
        &:after {
            content: '';
            background: @primaryColor;
            opacity: .7;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }
    .stickyItem {
        position: absolute;
        overflow: hidden;
        top: 0;
        width: 100vw;
        height: 100vh;
        img, video {
            position: absolute;
            top: 0;
            left: 0;
            object-fit: cover;
            width: 100%;
            height: 100%;
            object-position: center;
            .transform(scale(1.5));
        }
        .innerContent {
            width: 50%;
            height: auto;
            text-align: center;
            top: 50%;
            left: 50%;
            .transform(translate(-50%,-50%));
            z-index: 2;
        }
    }
    .socials {
        display: flex;
        flex-wrap: wrap;
        gap: @vw80;
        align-items: center;
        justify-content: center;
        .social {
            color: @hardWhite;
            text-decoration: none;
            .transitionMore(opacity, .3s);
            cursor: pointer;
            font-size: @vw25;
            padding: @vw20;
            &:hover {
                opacity: .5;
            }
        }
    }
    .imageRows {
        pointer-events: none;
    }
    .imageRow {
        display: block;
        position: relative;
        &:nth-child(1) {
            margin-bottom: @vw100 * 3;
            .imageWrapper {
                &:not(:last-child) {
                    margin-right: @vw106 + @vw16;
                }
                &:nth-child(1) {
                    margin-top: @vw100 * 2 + @vw65;
                    width: (@vw106 * 3) + (@vw16 * 3);
                    .innerImage {
                        .paddingRatio(384, 734);
                    }
                }
                &:nth-child(2) {
                    width: (@vw106 * 6) + (@vw16 * 5);
                    .innerImage {
                        .paddingRatio(716, 502);
                    }
                }
                &:nth-child(3) {
                    width: (@vw106 * 2) + @vw16;
                    .innerImage {
                        .paddingRatio(228, 446);
                    }
                }
            }
        }
        &:nth-child(2) {
            margin-bottom: @vw44;
            .imageWrapper {
                &:not(:last-child) {
                    margin-right: (@vw106 * 2) + (@vw16 * 3);
                }
                &:nth-child(1) {
                    margin-top: @vw100 + @vw20;
                    margin-left: (@vw106 * 2) + (@vw16 * 3);
                    width: (@vw106 * 4) + (@vw16 * 4);
                    .innerImage {
                        .paddingRatio(594, 515);
                    }
                }
                &:nth-child(2) {
                    width: (@vw106 * 4) + (@vw16 * 3);
                    .innerImage {
                        .paddingRatio(472, 711);
                    }
                }
            }
        }
        &:nth-child(3) {
            .imageWrapper {
                &:not(:last-child) {
                    margin-right: @vw106 + (@vw16 * 3);
                }
                &:nth-child(1) {
                    margin-left: @vw100 + @vw40;
                    width: (@vw106 * 5) + (@vw16 * 4);
                    .innerImage {
                        .paddingRatio(594, 389);
                    }
                }
                &:nth-child(2) {
                    margin-top: @vw100 * 2;
                    width: (@vw106 * 6) + (@vw16 * 5);
                    .innerImage {
                        .paddingRatio(716, 416);
                    }
                }
            }
        }
        .imageWrapper {
            display: inline-block;
            position: relative;
            height: auto;
            vertical-align: top;
            .transform(translate3d(0,0,0));
            &:after {
                opacity: .4;
            }
            .innerImage {
                height: 0;
                img {
                    position: absolute;
                    top: 0;
                    left: 0;
                    object-fit: cover;
                    width: 100%;
                    height: 100%;
                    object-position: center;
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .homeAboutBlock {
        padding-top: @vw100-1080 * 6;
        .socials {
            gap: @vw20-1080;
            .social {
                font-size: @vw25-1080;
                padding: @vw20-1080;
            }
        }
        .imageRow {
            &:nth-child(1) {
                margin-bottom: @vw100-1080 * 3;
                .imageWrapper {
                    &:not(:last-child) {
                        margin-right: @vw40-1080 + @vw16-1080;
                    }
                    &:nth-child(1) {
                        margin-top: @vw100-1080 * 2 + @vw65-1080;
                        width: (@vw106-1080 * 2) + (@vw16-1080 * 2);
                    }
                    &:nth-child(2) {
                        width: (@vw106-1080 * 4) + (@vw16-1080 * 3);
                    }
                    &:nth-child(3) {
                        width: (@vw106-1080 * 2) + @vw16-1080;
                    }
                }
            }
            &:nth-child(2) {
                margin-bottom: @vw44-1080;
                .imageWrapper {
                    &:not(:last-child) {
                        margin-right: (@vw106-1080 * 2) + (@vw16-1080 * 3);
                    }
                    &:nth-child(1) {
                        margin-top: @vw100-1080 + @vw20-1080;
                        margin-left: @vw40-1080;
                        width: (@vw106-1080 * 3) + (@vw16-1080 * 2);
                    }
                    &:nth-child(2) {
                        width: (@vw106-1080 * 2) + (@vw16-1080 * 2);
                    }
                }
            }
            &:nth-child(3) {
                display: none;
            }
        }
    }
}

@media all and (max-width: 580px) {
    .homeAboutBlock {
        padding-top: 0;
        .socials {
            gap: @vw40-580;
            flex-direction: row;
            .social {
                font-size: @vw25-580;
                padding: @vw20-580;
            }
        }
        .stickyItem {
            position: relative;
            overflow: hidden;
            top: auto;
            padding: @vw100-580 * 2 + @vw40-580 0;
            width: 100%;
            height: auto;
            .subTitle {
                margin: @vw20-580 0 @vw30-580 0;
            }
            img, video {
                position: absolute;
                top: 0;
                left: 0;
                object-fit: cover;
                width: 100%;
                height: 100%;
                object-position: center;
                .transform(scale(1));
            }
            .innerContent { 
                width: 100%;
                height: auto;
                top: auto;
                left: auto;
                .transform(translate(0,0));
            }
        }
    }
}