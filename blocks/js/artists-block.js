$(document).ready(function(){
    $(document).on("initPage", function () {
      if ($(".artistsBlock").length > 0) {
          initializeArtists();
      }
    });
});

function getUrlParam(name) {
  var results = new RegExp('[?&]' + name + '=([^&#]*)').exec(window.location.search);
  return results ? decodeURIComponent(results[1]) : null;
}

function updateArtistsUrlParam(type) {
  if (history.replaceState) {
    var url = new URL(window.location.href);
    url.searchParams.set('artists', type);
    history.replaceState(null, '', url.toString());
  }
}

function showArtistsFilter(type, animate = true) {
  var $block = $('.artistsBlock[data-init]');
  if (!$block.length) return;

  var $filters = $block.find('.filters .filter');
  var $artists = $block.find('.artists .artist');
  var $intros = $block.find('.artists .intro');

  $filters.removeClass('active');
  $filters.filter('[data-filter="' + type + '"]').addClass('active');
  $intros.hide();
  $intros.filter('[data-artist-type="' + type + '"]').show();
  var $toShow = $artists.filter('[data-artist-type="' + type + '"]');
  $artists.hide();

  if (animate && $toShow.length) {
    gsap.set($toShow, {opacity: 0, y: 40, display: 'block'});
    gsap.to($toShow, {
      opacity: 1,
      y: 0,
      stagger: 0.07,
      duration: 0.6,
      ease: 'power3.out',
      onStart: function() { $toShow.show(); }
    });
  } else {
    $toShow.show();
  }
  updateArtistsUrlParam(type);
}

function setupArtistsLayout() {
  var $block = $('.artistsBlock[data-init]');
  if (!$block.length) return;

  // Zorg dat partials naast elkaar staan (flex)
  $block.find('.artists').css({
    display: 'flex',
    flexWrap: 'wrap',
    gap: '32px', // pas aan naar wens
    alignItems: 'flex-start',
    justifyContent: 'flex-start'
  });
  $block.find('.partial').css({
    flex: '0 0 calc((100% - 32px * 3) / 4)', // 4 items per rij, rekening houdend met gap
    maxWidth: 'calc((100% - 32px * 3) / 4)',
    minWidth: '0',
    margin: '0',
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start'
  });
}

function applyInitialArtistsFilter() {
  var param = getUrlParam('artists');
  if (param === 'ones_to_watch' || param === 'main') {
    showArtistsFilter(param, false);
  } else {
    showArtistsFilter('main', false);
  }
}

function initializeArtists() {
  var $block = $('.artistsBlock[data-init]');
  if (!$block.length) return;

  setupArtistsLayout();

  var $filters = $block.find('.filters .filter');

  // Setup click handlers
  $filters.on('click', function() {
    var type = $(this).data('filter');
    showArtistsFilter(type);
  });

  // Apply initial filter
  applyInitialArtistsFilter();
}
