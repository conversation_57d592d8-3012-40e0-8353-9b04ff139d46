//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.password-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .password-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.13);
    backdrop-filter: blur(@vw50);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .password-modal-content {
    position: relative;
    background: white;
    .rounded(@vw15);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    
    .password-modal-header {
      padding: @vw30 @vw30 @vw20;
      border-bottom: 1px solid #eee;
      
      h3 {
        margin: 0;
        font-size: @vw18;
        font-weight: 800;
        color: @primaryColor;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-family: 'ApexMk2-Regular', Arial, sans-serif;
      }
    }
    
    .password-modal-body {
      padding: @vw30;
      
      p {
        margin: 0 0 @vw25 0;
        color: #333;
        font-size: @vw16;
        line-height: 1.5;
      }
      
      .password-input-container {
        margin-bottom: @vw30;
        
        input {
          width: 100%;
          padding: @vw15 @vw20;
          border: 2px solid #ddd;
          .rounded(@vw8);
          font-size: @vw16;
          font-family: 'ApexMk2-Regular', Arial, sans-serif;
          background: #f9f9f9;
          .transition(all, 0.3s);
          
          &:focus {
            outline: none;
            border-color: @primaryColor;
            background: white;
            box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.1);
          }
          
          &::placeholder {
            color: #999;
            text-transform: uppercase;
            font-size: @vw14;
            letter-spacing: 0.5px;
          }
        }
      }
      
      .password-modal-buttons {
        display: flex;
        gap: @vw15;
        justify-content: flex-end;
        
        .button-secondary {
          padding: @vw12 @vw25;
          background: transparent;
          border: 2px solid #ddd;
          color: #666;
          .rounded(@vw8);
          font-size: @vw14;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          cursor: pointer;
          .transition(all, 0.3s);
          font-family: 'ApexMk2-Regular', Arial, sans-serif;
          
          &:hover {
            border-color: #999;
            color: #333;
          }
        }
        
        .button-primary {
          padding: @vw12 @vw25;
          background: @primaryColor;
          border: 2px solid @primaryColor;
          color: white;
          .rounded(@vw8);
          font-size: @vw14;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          cursor: pointer;
          .transition(all, 0.3s);
          font-family: 'ApexMk2-Regular', Arial, sans-serif;
          
          &:hover {
            background: @primaryColorDark;
            border-color: @primaryColorDark;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        }
      }
      
      .password-error {
        margin-top: @vw20;
        padding: @vw15;
        background: #fee;
        border: 1px solid #fcc;
        .rounded(@vw8);
        color: #c33;
        font-size: @vw14;
        text-align: center;
      }
    }
  }
}

// Animation classes
.password-modal {
  opacity: 0;
  .transition(opacity, 0.3s);
  
  &.show {
    opacity: 1;
  }
  
  .password-modal-content {
    transform: scale(0.9) translateY(20px);
    .transition(transform, 0.3s);
  }
  
  &.show .password-modal-content {
    transform: scale(1) translateY(0);
  }
}

@media all and (max-width: 1080px) {
  .password-modal {
    .password-modal-overlay {
      backdrop-filter: blur(@vw50-1080);
    }
    .password-modal-content {
      .rounded(@vw15-1080);
      .password-modal-header {
        padding: @vw30-1080 @vw30-1080 @vw20-1080;
        h3 {
          font-size: @vw18-1080;
        }
      }
      .password-modal-body {
        padding: @vw30-1080;
        p {
          margin: 0 0 @vw25-1080 0;
          font-size: @vw16-1080;
        }
        .password-input-container {
          margin-bottom: @vw30-1080;
          input {
            padding: @vw15-1080 @vw20-1080;
            .rounded(@vw8-1080);
            font-size: @vw16-1080;
            &::placeholder {
              font-size: @vw14-1080;
            }
          }
        }
        .password-modal-buttons {
          gap: @vw15-1080;
          .button-secondary {
            padding: @vw12-1080 @vw25-1080;
            .rounded(@vw8-1080);
            font-size: @vw14-1080;
          }
          .button-primary {
            padding: @vw12-1080 @vw25-1080;
            .rounded(@vw8-1080);
            font-size: @vw14-1080;
          }
        }
        .password-error {
          margin-top: @vw20-1080;
          padding: @vw15-1080;
          .rounded(@vw8-1080);
          font-size: @vw14-1080;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .password-modal {
    .password-modal-overlay {
      backdrop-filter: blur(@vw50-580);
    }
    .password-modal-content {
      width: 95%;
      margin: @vw20-580;
      .rounded(@vw15-580);
      .password-modal-header {
        padding: @vw20-580 @vw20-580 @vw15-580;
        h3 {
          font-size: @vw16-580;
        }
      }
      .password-modal-body {
        padding: @vw20-580;
        p {
          font-size: @vw14-580;
          margin: 0 0 @vw25-580 0;
        }
        .password-input-container {
          margin-bottom: @vw30-580;
          input {
            font-size: @vw14-580;
            padding: @vw12-580 @vw15-580;
            .rounded(@vw8-580);
            &::placeholder {
              font-size: @vw14-580;
            }
          }
        }
        .password-modal-buttons {
          flex-direction: column;
          gap: @vw15-580;
          .button-secondary,
          .button-primary {
            width: 100%;
            justify-content: center;
            padding: @vw12-580 @vw25-580;
            .rounded(@vw8-580);
            font-size: @vw14-580;
          }
        }
        .password-error {
          margin-top: @vw20-580;
          padding: @vw15-580;
          .rounded(@vw8-580);
          font-size: @vw14-580;
        }
      }
    }
  }
}
