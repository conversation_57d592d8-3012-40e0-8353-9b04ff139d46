//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.teamHeaderBlock {
  padding: @vw100 + @vw40 0;
  position: relative;
  &.inview {
    .contactDetails {
      .button {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.3s 0.75s ease-out, transform 0.3s 0.75s ease-out;
        .stagger(20, 0.15s, .45s);
      }
    }
    .marqueeWrapper {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity 0.6s 0.45s ease-in-out, transform 0.6s 0.45s ease-in-out;
      .marquee {
        margin-bottom: -@vw22;
        white-space: normal;
        .item {
          margin-bottom: @vw22;
          white-space: nowrap;
          width: calc(33.3333% ~"-" @vw44);
        }
      }
    }
  }
  .backToTeam {
    position: absolute;
    top: @vw40;
    left: 0;
    color: @hardWhite;
    
    .arrows {
      color: @secondaryColorLight;
    }
    
    &:hover {
      .arrows {
        color: @hardWhite;
      }
    }
  }
  .normalTitle, .hugeTitle {
    text-align: center;
  }
  .memberPhotoWrapper {
    margin-bottom: @vw80;
    pointer-events: none;
    .pulse{
      position: absolute;
      animation: pulse-wave 4s linear infinite both;
      .rounded(@vw6);
      border: solid 1px @secondaryColorLight;
      position: absolute;
      top: 50%;
      left: 50%;
      height: 100%;
      width: 100%;
      .transform(translate(-50%, -50%) scale(1));
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 1s;
      }
      &:nth-child(3) {
        animation-delay: 2s;
      }
    }
    .memberPhoto {
      width: @vw100 + @vw30;
      height: @vw100 + @vw30;
      .rounded(@vw6);
      border: 1px solid @secondaryColorLight;
      margin: 0 auto;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
      }
    }
  }

  .normalTitle {
    margin-bottom: @vw10;
  }
  .marqueeWrapper {
    margin-top: @vw40;
  }
  .contactDetails {
    margin: auto;
    display: block;
    width: (@vw106 * 4) + (@vw16 * 3);
    max-width: 100%;
    margin-top: @vw50;
    .button {
      display: flex;
      .transform(translateY(@vw20));
      opacity: 0;
      &:not(:last-child) {
        margin-bottom: @vw12;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .teamHeaderBlock {
    padding: @vw100-1080 + @vw40-1080 0;
    .backToTeam {
      top: @vw40-1080;
    }
    .memberPhotoWrapper {
      margin-bottom: @vw80-1080;
      .pulse {
        .rounded(@vw6-1080);
      }
      .memberPhoto {
        width: @vw100-1080 + @vw30-1080;
        height: @vw100-1080 + @vw30-1080;
        .rounded(@vw6-1080);
      }
    }
    .normalTitle {
      margin-bottom: @vw10-1080;
    }
    .marqueeWrapper {
      margin-top: @vw40-1080;
      .marquee {
        margin-bottom: -@vw22-1080;
        .item {
          margin-bottom: @vw22-1080;
          width: calc(33.3333% ~"-" @vw44-1080);
        }
      }
    }
    .contactDetails {
      width: (@vw106-1080 * 4) + (@vw16-1080 * 3);
      margin-top: @vw50-1080;
      .button {
        .transform(translateY(@vw20-1080));
        &:not(:last-child) {
          margin-bottom: @vw12-1080;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .teamHeaderBlock {
    padding: (@vw100-580 * 1.5) 0 (@vw100-580 * 0.5);
    .backToTeam {
      top: @vw40-580;
    }
    .memberPhotoWrapper {
      margin: (@vw100-580 * 1) 0 @vw40-580 0;
      .pulse {
        .rounded(@vw6-580);
      }
      .memberPhoto {
        width: (@vw100-580 * 1.5);
        height: (@vw100-580 * 1.5);
        .rounded(@vw6-580);
      }
    }
    .memberName {
      font-size: (@vw100-580 * 0.8);
      margin-bottom: (@vw100-580 * 0.7);
    }
    .normalTitle {
      margin-bottom: @vw10-580;
    }
    .marqueeWrapper {
      margin-top: @vw40-580;
      .marquee {
        margin-bottom: -@vw22-580;
        .item {
          margin-bottom: @vw22-580;
          width: calc(50% ~"-" @vw44-580);
        }
      }
    }
    .contactDetails {
      max-width: 100%;
      padding: 0 @vw20-580;
      margin-top: @vw50-580;
      .button {
        .transform(translateY(@vw20-580));
        &:not(:last-child) {
          margin-bottom: @vw12-580;
        }
      }
      .contactItem {
        padding: @vw16-580 @vw20-580;
        .contactText {
          font-size: @vw14-580;
        }
        .contactIcon {
          width: @vw20-580;
          height: @vw20-580;
          i {
            font-size: @vw18-580;
          }
        }
      }
    }
  }
}

@keyframes pulse-wave{
  0%{
    opacity: .3;
    .transform(translate(-50%, -50%) scale(1));
  }
  100%{
    opacity: 0;
    .transform(translate(-50%, -50%) scale(2));
  }
}